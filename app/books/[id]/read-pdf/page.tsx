"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRout<PERSON> } from "next/navigation"
import { createSupabaseClient } from "@/lib/supabase/client"
import { PdfReader } from "@/components/PdfReader"
import { checkBookReadAccess } from "@/lib/utils/book-access"

interface Book {
  id: string
  title: string
  author_name: string
  ebook_file_type: string
}

interface Chapter {
  id: string
  title: string
  content: string
  chapter_number: number
  word_count: number
}

export default function ReadPdfPage() {
  const params = useParams()
  const router = useRouter()
  const [book, setBook] = useState<Book | null>(null)
  const [chapters, setChapters] = useState<Chapter[]>([])
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const supabase = createSupabaseClient()

  useEffect(() => {
    checkAccess()
  }, [params.id])

  const checkAccess = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser()
      setUser(user)

      if (!user) {
        router.push('/auth/login')
        return
      }

      // Check access using unified access checking
      const accessResult = await checkBookReadAccess(supabase, user.id, params.id)

      if (!accessResult.hasAccess || !accessResult.book) {
        router.push(`/books/${params.id}`)
        return
      }

      // Get book data
      const { data: bookData, error: bookError } = await supabase
        .from('projects')
        .select(`
          id,
          title,
          author_name,
          ebook_file_type
        `)
        .eq('id', params.id)
        .eq('is_ebook', true)
        .single()

      if (bookError) throw bookError
      
      // Only allow PDF books on this page
      if (bookData.ebook_file_type !== 'pdf') {
        router.push(`/books/${params.id}/read`)
        return
      }

      setBook(bookData)

      // Fetch chapters
      const { data: chaptersData, error: chaptersError } = await supabase
        .from('chapters')
        .select('*')
        .eq('project_id', params.id)
        .eq('is_published', true)
        .order('chapter_number', { ascending: true })

      if (chaptersError) throw chaptersError
      setChapters(chaptersData || [])

    } catch (error) {
      console.error('Error checking access:', error)
      router.push(`/books/${params.id}`)
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">📄</div>
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">Loading PDF...</h2>
          <p className="text-gray-600">Please wait while we prepare your reading experience.</p>
        </div>
      </div>
    )
  }

  if (!book || chapters.length === 0) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">📄</div>
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">PDF not available</h2>
          <p className="text-gray-600 mb-4">This PDF book is not ready for reading yet.</p>
        </div>
      </div>
    )
  }

  return (
    <PdfReader
      chapters={chapters}
      bookTitle={book.title}
      authorName={book.author_name}
      projectId={book.id}
      userId={user?.id}
      onClose={() => router.push('/library')}
    />
  )
}
