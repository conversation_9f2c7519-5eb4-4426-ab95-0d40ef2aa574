import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import pdf2json from 'pdf2json'
import { promisify } from 'util'

interface Chapter {
  id: string
  title: string
  pageNumber: number
  position: number
}

export async function POST(request: NextRequest) {
  try {
    const { projectId, fileUrl, chapters } = await request.json()

    if (!projectId || !fileUrl || !chapters || !Array.isArray(chapters)) {
      return NextResponse.json(
        { error: 'Project ID, file URL, and chapters array are required' },
        { status: 400 }
      )
    }

    if (chapters.length === 0) {
      return NextResponse.json(
        { error: 'At least one chapter must be defined' },
        { status: 400 }
      )
    }

    const supabase = await createSupabaseServerClient()
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Verify the project belongs to the user
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, title, user_id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      )
    }

    console.log(`Converting PDF to EPUB for project: ${projectId}`)
    console.log(`Chapters to create: ${chapters.length}`)

    // Download the PDF file
    const urlParts = fileUrl.split('/storage/v1/object/public/ebooks/')
    if (urlParts.length !== 2) {
      throw new Error('Invalid file URL format')
    }
    const filePath = urlParts[1]

    const { data: fileData, error: downloadError } = await supabase.storage
      .from('ebooks')
      .download(filePath)

    if (downloadError || !fileData) {
      throw new Error(`Failed to download PDF: ${downloadError?.message || 'Unknown error'}`)
    }

    // Convert PDF to text using pdf2json - extract page by page
    const arrayBuffer = await fileData.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    const pdfParser = new (pdf2json as any)()
    const parsePromise = promisify(pdfParser.parseBuffer.bind(pdfParser))

    await parsePromise(buffer)

    // Get the parsed PDF data structure
    const pdfData = pdfParser.getRawTextContent()
    const parsedData = JSON.parse(JSON.stringify(pdfParser))

    if (!pdfData || pdfData.trim().length === 0) {
      throw new Error('No text content could be extracted from the PDF')
    }

    // Extract text page by page from the parsed structure
    const pages = []
    const pdfPages = parsedData.Pages || []

    console.log(`Found ${pdfPages.length} pages in PDF structure`)

    for (let pageIndex = 0; pageIndex < pdfPages.length; pageIndex++) {
      const page = pdfPages[pageIndex]
      let pageText = ''

      if (page.Texts && Array.isArray(page.Texts)) {
        // Extract text from each text element on the page
        const textItems = []

        for (const textItem of page.Texts) {
          if (textItem.R && Array.isArray(textItem.R)) {
            for (const run of textItem.R) {
              if (run.T) {
                try {
                  const decodedText = decodeURIComponent(run.T)
                  textItems.push({
                    text: decodedText,
                    x: textItem.x || 0,
                    y: textItem.y || 0
                  })
                } catch (e) {
                  textItems.push({
                    text: run.T,
                    x: textItem.x || 0,
                    y: textItem.y || 0
                  })
                }
              }
            }
          }
        }

        // Sort by position (top to bottom, left to right)
        textItems.sort((a, b) => {
          const yDiff = a.y - b.y
          if (Math.abs(yDiff) > 0.5) return yDiff
          return a.x - b.x
        })

        pageText = textItems.map(item => item.text).join(' ')
      }

      pages.push(pageText.trim())
      console.log(`Page ${pageIndex + 1}: ${pageText.length} characters`)
    }
    
    console.log(`Extracted ${pages.length} pages from PDF`)

    // Sort chapters by page number
    const sortedChapters = [...chapters].sort((a, b) => a.pageNumber - b.pageNumber)

    // Create chapters based on user-defined page breaks
    const createdChapters = []
    
    for (let i = 0; i < sortedChapters.length; i++) {
      const chapter = sortedChapters[i]
      const nextChapter = sortedChapters[i + 1]
      
      const startPage = chapter.pageNumber - 1 // Convert to 0-based index
      const endPage = nextChapter ? nextChapter.pageNumber - 1 : pages.length

      console.log(`Creating chapter "${chapter.title}": pages ${startPage + 1} to ${endPage} (${endPage - startPage} pages)`)

      // Combine pages for this chapter
      let chapterContent = ''
      for (let pageIndex = startPage; pageIndex < endPage; pageIndex++) {
        if (pages[pageIndex]) {
          chapterContent += pages[pageIndex] + '\n\n'
          console.log(`  Added page ${pageIndex + 1}: ${pages[pageIndex].length} characters`)
        }
      }
      
      // Clean up the content
      chapterContent = chapterContent
        .replace(/\s+/g, ' ') // Normalize whitespace
        .replace(/\n\s*\n/g, '\n\n') // Clean up paragraph breaks
        .trim()
      
      if (chapterContent.length > 50) { // Only include chapters with substantial content
        createdChapters.push({
          title: chapter.title,
          content: chapterContent,
          chapter_number: i + 1,
          word_count: chapterContent.split(/\s+/).length
        })
      }
    }

    if (createdChapters.length === 0) {
      throw new Error('No chapters could be created from the selected pages')
    }

    console.log(`Created ${createdChapters.length} chapters`)

    // Clear existing chapters
    await supabase
      .from('chapters')
      .delete()
      .eq('project_id', projectId)

    // Insert new chapters
    const chaptersToInsert = createdChapters.map((chapter, index) => ({
      project_id: projectId,
      user_id: user.id,
      title: chapter.title,
      content: chapter.content,
      chapter_number: index + 1,
      word_count: chapter.word_count,
      is_published: true
    }))

    const { error: chaptersError } = await supabase
      .from('chapters')
      .insert(chaptersToInsert)

    if (chaptersError) {
      throw new Error(`Failed to save chapters: ${chaptersError.message}`)
    }

    // Calculate totals
    const totalWords = createdChapters.reduce((sum, ch) => sum + ch.word_count, 0)
    const readingTimeMinutes = Math.ceil(totalWords / 200) // Average reading speed
    const pageCount = Math.ceil(totalWords / 250) // Approximate pages

    // Update project with EPUB format and stats
    const { error: updateError } = await supabase
      .from('projects')
      .update({
        ebook_file_type: 'epub', // Convert to EPUB format
        total_chapters: createdChapters.length,
        total_words: totalWords,
        reading_time_minutes: readingTimeMinutes,
        page_count: pageCount,
        is_complete: true,
        status: 'published'
      })
      .eq('id', projectId)

    if (updateError) {
      throw new Error(`Failed to update project: ${updateError.message}`)
    }

    return NextResponse.json({
      success: true,
      message: 'PDF successfully converted to EPUB format',
      data: {
        chaptersCreated: createdChapters.length,
        totalWords,
        readingTime: readingTimeMinutes,
        pageCount
      }
    })

  } catch (error) {
    console.error('PDF to EPUB conversion error:', error)
    return NextResponse.json(
      { error: `Conversion failed: ${error.message}` },
      { status: 500 }
    )
  }
}
