import { NextRequest, NextResponse } from 'next/server'
import { createSupabaseServerClient } from '@/lib/supabase/client'
import pdf2json from 'pdf2json'
import { promisify } from 'util'

interface Chapter {
  id: string
  title: string
  pageNumber: number
  position: number
}

export async function POST(request: NextRequest) {
  try {
    const { projectId, fileUrl, chapters } = await request.json()

    if (!projectId || !fileUrl || !chapters || !Array.isArray(chapters)) {
      return NextResponse.json(
        { error: 'Project ID, file URL, and chapters array are required' },
        { status: 400 }
      )
    }

    if (chapters.length === 0) {
      return NextResponse.json(
        { error: 'At least one chapter must be defined' },
        { status: 400 }
      )
    }

    const supabase = await createSupabaseServerClient()
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Verify the project belongs to the user
    const { data: project, error: projectError } = await supabase
      .from('projects')
      .select('id, title, user_id')
      .eq('id', projectId)
      .eq('user_id', user.id)
      .single()

    if (projectError || !project) {
      return NextResponse.json(
        { error: 'Project not found or access denied' },
        { status: 404 }
      )
    }

    console.log(`Converting PDF to EPUB for project: ${projectId}`)
    console.log(`Chapters to create: ${chapters.length}`)

    // Download the PDF file
    const urlParts = fileUrl.split('/storage/v1/object/public/ebooks/')
    if (urlParts.length !== 2) {
      throw new Error('Invalid file URL format')
    }
    const filePath = urlParts[1]

    const { data: fileData, error: downloadError } = await supabase.storage
      .from('ebooks')
      .download(filePath)

    if (downloadError || !fileData) {
      throw new Error(`Failed to download PDF: ${downloadError?.message || 'Unknown error'}`)
    }

    // Convert PDF to text using pdf2json
    const arrayBuffer = await fileData.arrayBuffer()
    const buffer = Buffer.from(arrayBuffer)

    const pdfParser = new (pdf2json as any)()
    const parsePromise = promisify(pdfParser.parseBuffer.bind(pdfParser))
    
    await parsePromise(buffer)
    const pdfData = pdfParser.getRawTextContent()

    if (!pdfData || pdfData.trim().length === 0) {
      throw new Error('No text content could be extracted from the PDF')
    }

    // Split PDF text by pages (pdf2json separates pages with form feeds)
    const pages = pdfData.split('\f').filter(page => page.trim().length > 0)
    
    console.log(`Extracted ${pages.length} pages from PDF`)

    // Sort chapters by page number
    const sortedChapters = [...chapters].sort((a, b) => a.pageNumber - b.pageNumber)

    // Create chapters based on user-defined page breaks
    const createdChapters = []
    
    for (let i = 0; i < sortedChapters.length; i++) {
      const chapter = sortedChapters[i]
      const nextChapter = sortedChapters[i + 1]
      
      const startPage = chapter.pageNumber - 1 // Convert to 0-based index
      const endPage = nextChapter ? nextChapter.pageNumber - 1 : pages.length
      
      // Combine pages for this chapter
      let chapterContent = ''
      for (let pageIndex = startPage; pageIndex < endPage; pageIndex++) {
        if (pages[pageIndex]) {
          chapterContent += pages[pageIndex] + '\n\n'
        }
      }
      
      // Clean up the content
      chapterContent = chapterContent
        .replace(/\s+/g, ' ') // Normalize whitespace
        .replace(/\n\s*\n/g, '\n\n') // Clean up paragraph breaks
        .trim()
      
      if (chapterContent.length > 50) { // Only include chapters with substantial content
        createdChapters.push({
          title: chapter.title,
          content: chapterContent,
          chapter_number: i + 1,
          word_count: chapterContent.split(/\s+/).length
        })
      }
    }

    if (createdChapters.length === 0) {
      throw new Error('No chapters could be created from the selected pages')
    }

    console.log(`Created ${createdChapters.length} chapters`)

    // Clear existing chapters
    await supabase
      .from('chapters')
      .delete()
      .eq('project_id', projectId)

    // Insert new chapters
    const chaptersToInsert = createdChapters.map((chapter, index) => ({
      project_id: projectId,
      user_id: user.id,
      title: chapter.title,
      content: chapter.content,
      chapter_number: index + 1,
      word_count: chapter.word_count,
      is_published: true
    }))

    const { error: chaptersError } = await supabase
      .from('chapters')
      .insert(chaptersToInsert)

    if (chaptersError) {
      throw new Error(`Failed to save chapters: ${chaptersError.message}`)
    }

    // Calculate totals
    const totalWords = createdChapters.reduce((sum, ch) => sum + ch.word_count, 0)
    const readingTimeMinutes = Math.ceil(totalWords / 200) // Average reading speed
    const pageCount = Math.ceil(totalWords / 250) // Approximate pages

    // Update project with EPUB format and stats
    const { error: updateError } = await supabase
      .from('projects')
      .update({
        ebook_file_type: 'epub', // Convert to EPUB format
        total_chapters: createdChapters.length,
        total_words: totalWords,
        reading_time_minutes: readingTimeMinutes,
        page_count: pageCount,
        is_complete: true,
        status: 'published'
      })
      .eq('id', projectId)

    if (updateError) {
      throw new Error(`Failed to update project: ${updateError.message}`)
    }

    return NextResponse.json({
      success: true,
      message: 'PDF successfully converted to EPUB format',
      data: {
        chaptersCreated: createdChapters.length,
        totalWords,
        readingTime: readingTimeMinutes,
        pageCount
      }
    })

  } catch (error) {
    console.error('PDF to EPUB conversion error:', error)
    return NextResponse.json(
      { error: `Conversion failed: ${error.message}` },
      { status: 500 }
    )
  }
}
