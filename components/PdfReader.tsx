"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ChevronLeft, ChevronRight, BookOpen, Settings, Bookmark, Search, Highlighter, MessageCircle, X, Palette, Type } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { useNavigation } from "@/contexts/NavigationContext"

interface PdfPage {
  id: string
  pageNumber: number
  content: string
  textContent: string
}

interface Highlight {
  id: string
  start_position: number
  end_position: number
  selected_text: string
  color: string
  is_private: boolean
  user_id: string
  created_at: string
}

interface MarginComment {
  id: string
  content: string
  position_data: {
    startPos: number
    endPos: number
    anchorText: string
  }
  user_id: string
  parent_id?: string
  created_at: string
  replies?: MarginComment[]
}

interface PdfReaderProps {
  pdfUrl: string
  bookTitle: string
  authorName: string
  projectId: string
  userId?: string
  onClose: () => void
  isPreview?: boolean
  onApprovePreview?: () => void
}

export function PdfReader({ pdfUrl, bookTitle, authorName, projectId, userId, onClose, isPreview = false, onApprovePreview }: PdfReaderProps) {
  const supabase = createSupabaseClient()
  const { hideNavigation, showNavigation } = useNavigation()

  // Core reader state
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [fontSize, setFontSize] = useState(16)
  const [fontFamily, setFontFamily] = useState('serif')
  const [lineHeight, setLineHeight] = useState(1.6)
  const [theme, setTheme] = useState('light')
  const [showSettings, setShowSettings] = useState(false)
  const [showPageList, setShowPageList] = useState(false)
  const [showFontSizeMenu, setShowFontSizeMenu] = useState(false)
  const [readingProgress, setReadingProgress] = useState(0)
  const [bookmarks, setBookmarks] = useState<number[]>([])
  const [zoom, setZoom] = useState(1.0)

  // PDF-specific state
  const [pdfDoc, setPdfDoc] = useState<any>(null)
  const [pageContent, setPageContent] = useState<string>('')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Social features state
  const [highlights, setHighlights] = useState<Highlight[]>([])
  const [marginComments, setMarginComments] = useState<MarginComment[]>([])
  const [selectedText, setSelectedText] = useState<{
    text: string
    startPos: number
    endPos: number
  } | null>(null)
  const [showHighlightMenu, setShowHighlightMenu] = useState(false)
  const [showCommentForm, setShowCommentForm] = useState(false)
  const [highlightColor, setHighlightColor] = useState('#ffeb3b')
  const [newComment, setNewComment] = useState('')
  const [isPageTurning, setIsPageTurning] = useState(false)

  // Refs for text selection and page animation
  const contentRef = useRef<HTMLDivElement>(null)
  const pageRef = useRef<HTMLDivElement>(null)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  return (
    <div className={`min-h-screen ${theme === 'dark' ? 'bg-gray-900 text-white' : 'bg-white text-gray-900'}`}>
      {/* Header - IDENTICAL to EPUB reader */}
      <div className="sticky top-0 z-50 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
        <div className="flex items-center justify-between max-w-6xl mx-auto">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="flex items-center space-x-2"
            >
              <ChevronLeft className="h-4 w-4" />
              <span>Library</span>
            </Button>

            <div className="hidden md:block">
              <h1 className="font-semibold text-lg">{title}</h1>
              <p className="text-sm text-gray-600 dark:text-gray-400">{author}</p>
            </div>
          </div>

          <div className="flex items-center space-x-4">
            {/* Page Navigation - like EPUB reader */}
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>

              <div className="flex items-center space-x-1">
                <input
                  type="number"
                  value={currentPage}
                  onChange={(e) => setCurrentPage(parseInt(e.target.value) || 1)}
                  className="w-16 px-2 py-1 text-sm border rounded text-center"
                  min="1"
                  max={totalPages}
                />
                <span className="text-sm text-gray-600">of {totalPages}</span>
              </div>

              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage === totalPages}
              >
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>

            {/* Zoom Controls */}
            <div className="flex items-center space-x-1">
              <Button variant="ghost" size="sm">
                <Minus className="h-4 w-4" />
              </Button>
              <span className="text-sm w-12 text-center">100%</span>
              <Button variant="ghost" size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(!showSettings)}
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Settings Panel - Simplified */}
      {showSettings && (
        <div className="sticky top-16 z-40 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-4">
          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium mb-2">Theme</label>
                <select
                  value={theme}
                  onChange={(e) => setTheme(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="light">Light</option>
                  <option value="dark">Dark</option>
                  <option value="sepia">Sepia</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Font Size: {fontSize}px</label>
                <input
                  type="range"
                  min="12"
                  max="24"
                  value={fontSize}
                  onChange={(e) => setFontSize(parseInt(e.target.value))}
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Font Family</label>
                <select
                  value={fontFamily}
                  onChange={(e) => setFontFamily(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md"
                >
                  <option value="Georgia">Georgia</option>
                  <option value="Times New Roman">Times New Roman</option>
                  <option value="Arial">Arial</option>
                  <option value="Helvetica">Helvetica</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* PDF Content - Styled like EPUB reader */}
      <div className="flex-1 pb-16">
        <div className="max-w-4xl mx-auto px-4 py-8">
          <div className="bg-white dark:bg-gray-800 shadow-lg rounded-lg overflow-hidden">
            <iframe
              src={pdfViewerUrl}
              className="w-full border-0"
              title={title}
              style={{ height: 'calc(100vh - 200px)' }}
            />
          </div>
        </div>
      </div>

      {/* Bottom Navigation - IDENTICAL to EPUB reader */}
      <div className="fixed bottom-0 left-0 right-0 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 px-4 py-3">
        <div className="flex items-center justify-between max-w-6xl mx-auto">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
            disabled={currentPage === 1}
            className="flex items-center space-x-2"
          >
            <ChevronLeft className="h-4 w-4" />
            <span className="hidden sm:inline">Previous</span>
          </Button>

          <div className="flex items-center space-x-4">
            <span className="text-sm">Page {currentPage} of {totalPages}</span>
          </div>

          <Button
            variant="ghost"
            size="sm"
            onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
            disabled={currentPage === totalPages}
            className="flex items-center space-x-2"
          >
            <span className="hidden sm:inline">Next</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
