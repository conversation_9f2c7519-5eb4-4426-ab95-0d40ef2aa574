"use client"

import { useEffect, useCallback } from "react"
import { Button } from "@/components/ui/button"
import { X } from "lucide-react"
import { useNavigation } from "@/contexts/NavigationContext"

interface SimplePdfViewerProps {
  pdfUrl: string
  title: string
  author: string
  onClose: () => void
  onApprove?: () => void
}

export function SimplePdfViewer({ pdfUrl, title, author, onClose, onApprove }: SimplePdfViewerProps) {
  const { hideNavigation, showNavigation } = useNavigation()

  // Hide navigation when viewer opens, show when it closes
  useEffect(() => {
    hideNavigation()
    return () => {
      showNavigation()
    }
  }, [hideNavigation, showNavigation])

  // Wrapper function to ensure navigation is shown when closing
  const handleClose = useCallback(() => {
    showNavigation()
    onClose()
  }, [showNavigation, onClose])

  const handleApprove = useCallback(() => {
    showNavigation()
    if (onApprove) {
      onApprove()
    }
  }, [showNavigation, onApprove])

  return (
    <div className="fixed inset-0 z-50 bg-white">
      {/* Header */}
      <div className="border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="font-semibold text-lg">{title}</h1>
            <p className="text-sm text-gray-600">by {author}</p>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClose}
            className="flex items-center space-x-2"
          >
            <X className="h-4 w-4" />
            <span>Close</span>
          </Button>
        </div>
      </div>

      {/* PDF Viewer */}
      <div className="flex-1 h-full">
        <iframe
          src={pdfUrl}
          className="w-full h-full border-0"
          title={title}
          style={{ height: 'calc(100vh - 80px)' }}
        />
      </div>

      {/* Floating Action Buttons for Preview Mode */}
      {onApprove && (
        <div className="fixed bottom-6 right-6 z-50 flex flex-col gap-3">
          {/* Close Preview Button */}
          <Button
            onClick={handleClose}
            variant="outline"
            className="bg-white hover:bg-gray-50 text-gray-700 shadow-lg px-6 py-3 text-base font-semibold rounded-full border-2"
            size="lg"
          >
            ← Close Preview
          </Button>

          {/* Approve & Publish Button */}
          <Button
            onClick={handleApprove}
            className="bg-green-600 hover:bg-green-700 text-white shadow-lg px-6 py-3 text-base font-semibold rounded-full"
            size="lg"
          >
            ✓ Approve & Publish Book
          </Button>
        </div>
      )}
    </div>
  )
}

export default SimplePdfViewer
