"use client"

import { useState, useRef, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { X, Plus, Edit2, Trash2 } from "lucide-react"
import { useNavigation } from "@/contexts/NavigationContext"

interface Chapter {
  id: string
  title: string
  pageNumber: number
  position: number
}

interface PdfChapterMarkerProps {
  pdfUrl: string
  title: string
  author: string
  onClose: () => void
  onConvert: (chapters: Chapter[]) => void
  isPreview?: boolean
}

export function PdfChapterMarker({ pdfUrl, title, author, onClose, onConvert, isPreview = false }: PdfChapterMarkerProps) {
  const { hideNavigation, showNavigation } = useNavigation()
  const [chapters, setChapters] = useState<Chapter[]>([])
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [markingMode, setMarkingMode] = useState(false)
  const [editingChapter, setEditingChapter] = useState<string | null>(null)
  const [editTitle, setEditTitle] = useState("")
  const iframeRef = useRef<HTMLIFrameElement>(null)

  useEffect(() => {
    hideNavigation()
    return () => showNavigation()
  }, [hideNavigation, showNavigation])

  const addChapter = () => {
    const newChapter: Chapter = {
      id: Date.now().toString(),
      title: `Chapter ${chapters.length + 1}`,
      pageNumber: currentPage,
      position: currentPage
    }
    setChapters(prev => [...prev, newChapter].sort((a, b) => a.position - b.position))
  }

  const goToPage = (pageNum: number) => {
    setCurrentPage(pageNum)
  }

  const removeChapter = (id: string) => {
    setChapters(prev => prev.filter(c => c.id !== id))
  }

  const startEdit = (chapter: Chapter) => {
    setEditingChapter(chapter.id)
    setEditTitle(chapter.title)
  }

  const saveEdit = () => {
    if (editingChapter) {
      setChapters(prev => prev.map(c => 
        c.id === editingChapter ? { ...c, title: editTitle } : c
      ))
      setEditingChapter(null)
      setEditTitle("")
    }
  }

  const cancelEdit = () => {
    setEditingChapter(null)
    setEditTitle("")
  }

  const handleConvert = () => {
    if (isPreview) {
      if (chapters.length < 2) {
        alert("For preview, please mark Chapter 1 and Chapter 2 so we know where Chapter 1 ends.")
        return
      }
    } else {
      if (chapters.length === 0) {
        alert("Please mark at least one chapter before converting.")
        return
      }
    }
    onConvert(chapters)
  }

  return (
    <div className="fixed inset-0 z-50 bg-white flex">
      {/* PDF Viewer */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <div className="border-b border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="font-semibold text-lg">{title}</h1>
              <p className="text-sm text-gray-600">by {author}</p>
            </div>
            <div className="flex items-center gap-3">
              <span className="text-sm text-gray-500">
                Page {currentPage} of {totalPages}
              </span>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="flex items-center space-x-2"
              >
                <X className="h-4 w-4" />
                <span>Close</span>
              </Button>
            </div>
          </div>
        </div>

        {/* PDF Display */}
        <div className="flex-1 relative">
          <iframe
            ref={iframeRef}
            src={`${pdfUrl}#page=${currentPage}`}
            className="w-full h-full border-0"
            title={title}
          />
          
          {markingMode && (
            <div className="absolute inset-0 bg-blue-500/10 flex items-center justify-center pointer-events-none">
              <div className="bg-blue-600 text-white px-4 py-2 rounded-lg shadow-lg">
                Click "Mark Chapter" to add a chapter at page {currentPage}
              </div>
            </div>
          )}
        </div>

        {/* Controls */}
        <div className="border-t border-gray-200 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Button
                onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                disabled={currentPage <= 1}
                variant="outline"
                size="sm"
              >
                ← Previous
              </Button>
              <Button
                onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                disabled={currentPage >= totalPages}
                variant="outline"
                size="sm"
              >
                Next →
              </Button>
            </div>

            <div className="flex items-center gap-3">
              <Button
                onClick={() => setMarkingMode(!markingMode)}
                variant={markingMode ? "default" : "outline"}
                size="sm"
              >
                {markingMode ? "Exit Marking" : "Mark Chapters"}
              </Button>
              {markingMode && (
                <Button
                  onClick={addChapter}
                  className="bg-red-600 text-white hover:bg-red-700"
                  size="sm"
                >
                  <Plus className="h-4 w-4 mr-1" />
                  Mark Chapter
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Chapter List Sidebar */}
      <div className="w-80 border-l border-gray-200 bg-gray-50 flex flex-col">
        <div className="p-4 border-b border-gray-200">
          <h2 className="font-semibold text-lg">
            {isPreview ? 'Preview Setup' : `Chapters (${chapters.length})`}
          </h2>
          <p className="text-sm text-gray-600 mt-1">
            {isPreview
              ? 'Mark Chapter 1 and Chapter 2 to preview Chapter 1 in the e-reader'
              : 'Mark where each chapter begins in your PDF'
            }
          </p>
        </div>

        <div className="flex-1 overflow-y-auto p-4 space-y-3">
          {chapters.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <div className="text-4xl mb-2">📖</div>
              <p>No chapters marked yet</p>
              <p className="text-sm mt-1">Click "Mark Chapters" to start</p>
            </div>
          ) : (
            chapters.map((chapter, index) => (
              <div key={chapter.id} className="bg-white rounded-lg p-3 border border-gray-200">
                {editingChapter === chapter.id ? (
                  <div className="space-y-2">
                    <input
                      type="text"
                      value={editTitle}
                      onChange={(e) => setEditTitle(e.target.value)}
                      className="w-full px-2 py-1 border border-gray-300 rounded text-sm"
                      placeholder="Chapter title"
                      autoFocus
                    />
                    <div className="flex gap-2">
                      <Button onClick={saveEdit} size="sm" className="flex-1">
                        Save
                      </Button>
                      <Button onClick={cancelEdit} variant="outline" size="sm" className="flex-1">
                        Cancel
                      </Button>
                    </div>
                  </div>
                ) : (
                  <div>
                    <div className="flex items-start justify-between">
                      <div
                        className="flex-1 cursor-pointer hover:bg-gray-50 p-1 rounded"
                        onClick={() => goToPage(chapter.pageNumber)}
                        title="Click to go to this page"
                      >
                        <h3 className="font-medium text-sm">{chapter.title}</h3>
                        <p className="text-xs text-gray-500">Page {chapter.pageNumber} (click to view)</p>
                      </div>
                      <div className="flex gap-1">
                        <button
                          onClick={() => startEdit(chapter)}
                          className="p-1 text-gray-400 hover:text-gray-600"
                        >
                          <Edit2 className="h-3 w-3" />
                        </button>
                        <button
                          onClick={() => removeChapter(chapter.id)}
                          className="p-1 text-gray-400 hover:text-red-600"
                        >
                          <Trash2 className="h-3 w-3" />
                        </button>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            ))
          )}
        </div>

        <div className="p-4 border-t border-gray-200">
          <Button
            onClick={handleConvert}
            disabled={isPreview ? chapters.length < 2 : chapters.length === 0}
            className="w-full bg-green-600 text-white hover:bg-green-700"
          >
            {isPreview
              ? `Preview Chapter 1 (${chapters.length}/2 marked)`
              : `Convert to EPUB (${chapters.length} chapters)`
            }
          </Button>
        </div>
      </div>
    </div>
  )
}
