"use client"

import { useState, useEffect, useRef, useCallback } from "react"
import { createSupabaseClient } from "@/lib/supabase/client"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { ChevronLeft, ChevronRight, BookOpen, Settings, Bookmark, Highlighter, MessageCircle, X, Type } from "lucide-react"
import { motion, AnimatePresence } from "framer-motion"
import { useNavigation } from "@/contexts/NavigationContext"

interface Highlight {
  id: string
  start_position: number
  end_position: number
  selected_text: string
  color: string
  is_private: boolean
  user_id: string
  created_at: string
}

interface MarginComment {
  id: string
  content: string
  position_data: {
    startPos: number
    endPos: number
    anchorText: string
  }
  user_id: string
  parent_id?: string
  created_at: string
  replies?: MarginComment[]
}

interface PdfPreviewReaderProps {
  bookTitle: string
  authorName: string
  projectId: string
  userId?: string
  onClose: () => void
  isPreview?: boolean
  onApprovePreview?: () => void
  previewContent: string // The extracted first chapter/page content
}

export function PdfPreviewReader({ 
  bookTitle, 
  authorName, 
  projectId, 
  userId, 
  onClose, 
  isPreview = true, 
  onApprovePreview,
  previewContent 
}: PdfPreviewReaderProps) {
  const supabase = createSupabaseClient()
  const { hideNavigation, showNavigation } = useNavigation()

  // Core reader state - simplified for preview
  const [fontSize, setFontSize] = useState(16)
  const [fontFamily, setFontFamily] = useState('serif')
  const [lineHeight, setLineHeight] = useState(1.6)
  const [theme, setTheme] = useState('light')
  const [showSettings, setShowSettings] = useState(false)
  const [showFontSizeMenu, setShowFontSizeMenu] = useState(false)

  // Social features state (disabled for preview but UI remains)
  const [highlights, setHighlights] = useState<Highlight[]>([])
  const [marginComments, setMarginComments] = useState<MarginComment[]>([])
  const [selectedText, setSelectedText] = useState<{
    text: string
    startPos: number
    endPos: number
  } | null>(null)
  const [showHighlightMenu, setShowHighlightMenu] = useState(false)
  const [showCommentForm, setShowCommentForm] = useState(false)
  const [highlightColor, setHighlightColor] = useState('#ffeb3b')
  const [newComment, setNewComment] = useState('')

  // Refs for text selection
  const contentRef = useRef<HTMLDivElement>(null)
  const pageRef = useRef<HTMLDivElement>(null)

  // Hide navigation when reader opens, show when it closes
  useEffect(() => {
    hideNavigation()
    return () => {
      showNavigation()
    }
  }, [hideNavigation, showNavigation])

  // Wrapper function to ensure navigation is shown when closing
  const handleClose = useCallback(() => {
    showNavigation()
    onClose()
  }, [showNavigation, onClose])

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (!target.closest('.font-size-dropdown')) {
        setShowFontSizeMenu(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [])

  // Text selection handling (disabled for preview but UI remains)
  const handleTextSelection = useCallback(() => {
    if (!isPreview) return // Disable selection in preview mode
    
    const selection = window.getSelection()
    if (!selection || selection.rangeCount === 0) return

    const range = selection.getRangeAt(0)
    const selectedText = selection.toString().trim()

    if (selectedText.length > 0 && contentRef.current?.contains(range.commonAncestorContainer)) {
      const startPos = range.startOffset
      const endPos = range.endOffset

      setSelectedText({
        text: selectedText,
        startPos,
        endPos
      })
      setShowHighlightMenu(true)
    }
  }, [isPreview])

  const getThemeClasses = () => {
    switch (theme) {
      case 'dark':
        return 'bg-gray-900 text-gray-100'
      case 'sepia':
        return 'bg-yellow-50 text-yellow-900'
      default:
        return 'bg-white text-gray-900'
    }
  }

  // Render text with basic formatting (similar to EPUB reader)
  const renderFormattedText = (text: string) => {
    return text
      .split('\n')
      .map((line, index) => {
        // Handle headings
        if (line.startsWith('# ')) {
          return <h1 key={index} className="text-2xl font-bold mb-4 mt-6">{line.substring(2)}</h1>
        }
        if (line.startsWith('## ')) {
          return <h2 key={index} className="text-xl font-bold mb-3 mt-5">{line.substring(3)}</h2>
        }
        if (line.startsWith('### ')) {
          return <h3 key={index} className="text-lg font-bold mb-2 mt-4">{line.substring(4)}</h3>
        }

        // Handle blockquotes
        if (line.startsWith('> ')) {
          return <blockquote key={index} className="border-l-4 border-gray-300 pl-4 italic my-2">{line.substring(2)}</blockquote>
        }

        // Handle list items
        if (line.startsWith('• ')) {
          return <li key={index} className="ml-4 mb-1">{line.substring(2)}</li>
        }

        // Handle empty lines
        if (line.trim() === '') {
          return <br key={index} />
        }

        // Handle regular paragraphs with inline formatting
        const formattedLine = line
          .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
          .replace(/\*(.*?)\*/g, '<em>$1</em>')

        return (
          <p
            key={index}
            className="mb-4 leading-relaxed"
            dangerouslySetInnerHTML={{ __html: formattedLine }}
          />
        )
      })
  }

  if (!previewContent) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="w-full max-w-md mx-4">
          <CardContent className="p-6 text-center">
            <div className="text-red-500 text-4xl mb-4">📄</div>
            <h3 className="text-lg font-semibold mb-2">No Preview Available</h3>
            <p className="text-gray-600 mb-4">This PDF doesn't have any readable content yet.</p>
            <Button onClick={handleClose}>Close Reader</Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={`fixed inset-0 z-50 ${getThemeClasses()}`}>
      {/* Header - IDENTICAL to EPUB reader */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        {/* Mobile Header - Stacked Layout */}
        <div className="block sm:hidden">
          <div
            className="flex items-center justify-between p-3 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
            onClick={handleClose}
            title="Click to close reader"
          >
            <div>
              <h1
                className="font-semibold text-base"
                style={{
                  maxWidth: '180px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
                title={bookTitle}
              >
                {bookTitle}
              </h1>
              <p className="text-sm opacity-70 truncate">by {authorName}</p>
            </div>
            <div
              className="flex items-center space-x-1"
              onClick={(e) => e.stopPropagation()}
            >
              <Button
                variant="ghost"
                size="sm"
                onClick={() => window.location.href = '/library'}
                title="Go to Library"
              >
                📚
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="opacity-50 cursor-not-allowed"
                title="Bookmarks (Preview Mode)"
              >
                <Bookmark className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="opacity-50 cursor-not-allowed"
                title="Highlights (Preview Mode)"
              >
                <Highlighter className="h-4 w-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                className="opacity-50 cursor-not-allowed"
                title="Comments (Preview Mode)"
              >
                <MessageCircle className="h-4 w-4" />
              </Button>
              {/* Font Size Dropdown */}
              <div className="relative font-size-dropdown">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowFontSizeMenu(!showFontSizeMenu)}
                  title="Font Size"
                >
                  <Type className="h-4 w-4" />
                </Button>
                {showFontSizeMenu && (
                  <div className="absolute bottom-full right-0 mb-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3 z-50">
                    <div className="flex flex-col space-y-2 min-w-[140px]">
                      <div className="text-xs font-medium text-center text-gray-600 dark:text-gray-400">Font Size</div>
                      <input
                        type="range"
                        min="12"
                        max="24"
                        step="2"
                        value={fontSize}
                        onChange={(e) => setFontSize(parseInt(e.target.value))}
                        className="w-full"
                      />
                      <div className="text-xs text-center font-medium">{fontSize}px</div>
                    </div>
                  </div>
                )}
              </div>
              <Button variant="ghost" size="sm" onClick={() => setShowSettings(!showSettings)} title="Reader Settings">
                <Settings className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Desktop Header - Horizontal Layout */}
        <div className="hidden sm:flex items-center justify-between p-4">
          <div
            className="flex items-center space-x-4 cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors rounded-lg p-2 -m-2"
            onClick={handleClose}
            title="Click to close reader"
          >
            <div>
              <h1
                className="font-semibold text-lg"
                style={{
                  maxWidth: '300px',
                  overflow: 'hidden',
                  textOverflow: 'ellipsis',
                  whiteSpace: 'nowrap'
                }}
                title={bookTitle}
              >
                {bookTitle}
              </h1>
              <p className="text-sm opacity-70">by {authorName}</p>
            </div>
          </div>

          <div
            className="flex items-center space-x-2"
            onClick={(e) => e.stopPropagation()}
          >
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.location.href = '/library'}
              title="Go to Library"
            >
              📚
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="opacity-50 cursor-not-allowed"
              title="Bookmarks (Preview Mode)"
            >
              <Bookmark className="h-4 w-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="opacity-50 cursor-not-allowed"
              title="Highlights (Preview Mode)"
            >
              <Highlighter className="h-4 w-4" />
            </Button>

            <Button
              variant="ghost"
              size="sm"
              className="opacity-50 cursor-not-allowed"
              title="Comments (Preview Mode)"
            >
              <MessageCircle className="h-4 w-4" />
            </Button>

            {/* Font Size Dropdown */}
            <div className="relative font-size-dropdown">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowFontSizeMenu(!showFontSizeMenu)}
                title="Font Size"
              >
                <Type className="h-4 w-4" />
              </Button>
              {showFontSizeMenu && (
                <div className="absolute top-full right-0 mt-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg p-3 z-50">
                  <div className="flex flex-col space-y-2 min-w-[140px]">
                    <div className="text-xs font-medium text-center text-gray-600 dark:text-gray-400">Font Size</div>
                    <input
                      type="range"
                      min="12"
                      max="24"
                      step="2"
                      value={fontSize}
                      onChange={(e) => setFontSize(parseInt(e.target.value))}
                      className="w-full"
                    />
                    <div className="text-xs text-center font-medium">{fontSize}px</div>
                  </div>
                </div>
              )}
            </div>
            <Button variant="ghost" size="sm" onClick={() => setShowSettings(!showSettings)} title="Reader Settings">
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Enhanced Progress Bar - Shows preview mode */}
      <div className="px-4 py-2 border-b border-gray-200 dark:border-gray-700">
        <div className="h-1 bg-gray-200 dark:bg-gray-700 rounded-full">
          <div
            className="h-full bg-blue-600 transition-all duration-300 rounded-full"
            style={{ width: '100%' }}
          />
        </div>
        <div className="text-xs opacity-70 mt-1 text-center">
          Preview Mode • First Chapter Only
        </div>
      </div>

      <div className="flex h-full">
        {/* Settings Sidebar - IDENTICAL to EPUB reader */}
        {showSettings && (
          <div className="w-80 border-r border-gray-200 dark:border-gray-700 overflow-y-auto">
            <div className="p-4">
              <h3 className="font-semibold mb-4">Reading Settings</h3>

              <div className="space-y-6">
                {/* Font Family */}
                <div>
                  <label className="block text-sm font-medium mb-2">Font Family</label>
                  <select
                    value={fontFamily}
                    onChange={(e) => setFontFamily(e.target.value)}
                    className="w-full p-2 border rounded-md bg-background"
                  >
                    <option value="serif">Serif</option>
                    <option value="sans-serif">Sans Serif</option>
                    <option value="monospace">Monospace</option>
                  </select>
                </div>

                {/* Line Height */}
                <div>
                  <label className="block text-sm font-medium mb-2">Line Height</label>
                  <input
                    type="range"
                    min="1.2"
                    max="2.0"
                    step="0.1"
                    value={lineHeight}
                    onChange={(e) => setLineHeight(parseFloat(e.target.value))}
                    className="w-full"
                  />
                  <span className="text-sm">{lineHeight}</span>
                </div>

                {/* Theme */}
                <div>
                  <label className="block text-sm font-medium mb-2">Theme</label>
                  <div className="grid grid-cols-3 gap-2">
                    <Button
                      variant={theme === 'light' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setTheme('light')}
                    >
                      Light
                    </Button>
                    <Button
                      variant={theme === 'dark' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setTheme('dark')}
                    >
                      Dark
                    </Button>
                    <Button
                      variant={theme === 'sepia' ? 'default' : 'outline'}
                      size="sm"
                      onClick={() => setTheme('sepia')}
                    >
                      Sepia
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Main Reading Area - IDENTICAL to EPUB reader */}
        <div className="flex-1 flex flex-col relative">
          <div
            className="flex-1 overflow-y-auto"
            ref={pageRef}
          >
            <div className="max-w-4xl mx-auto p-8 relative">
              <div className="mb-8">
                <h2
                  className="text-2xl font-bold mb-2"
                  style={{
                    fontSize: `${fontSize + 8}px`,
                    fontFamily,
                    lineHeight
                  }}
                >
                  Preview - First Chapter
                </h2>
                <div className="text-sm opacity-70">
                  PDF Preview • This is a sample of the content
                </div>
              </div>

              <div
                ref={contentRef}
                className="prose prose-lg max-w-none relative"
                style={{
                  fontSize: `${fontSize}px`,
                  fontFamily,
                  lineHeight
                }}
                onMouseUp={handleTextSelection}
                onTouchEnd={handleTextSelection}
              >
                {renderFormattedText(previewContent)}
              </div>
            </div>
          </div>

          {/* Navigation Footer - Simplified for preview */}
          <div className="border-t border-gray-200 dark:border-gray-700 p-4">
            <div className="flex items-center justify-center max-w-4xl mx-auto">
              <div className="text-sm opacity-70">
                Preview Mode - Full book available after purchase
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Floating Action Buttons for Preview Mode */}
      {isPreview && (
        <div className="fixed bottom-6 right-6 z-50 flex flex-col gap-3">
          {/* Back to Upload Button */}
          <Button
            onClick={onClose}
            variant="outline"
            className="bg-white hover:bg-gray-50 text-gray-700 shadow-lg px-6 py-3 text-base font-semibold rounded-full border-2"
            size="lg"
          >
            ← Back to Upload
          </Button>

          {/* Approve & Publish Button */}
          {onApprovePreview && (
            <Button
              onClick={onApprovePreview}
              className="bg-green-600 hover:bg-green-700 text-white shadow-lg px-6 py-3 text-base font-semibold rounded-full"
              size="lg"
            >
              ✓ Approve & Publish Book
            </Button>
          )}
        </div>
      )}
    </div>
  )
}

export default PdfPreviewReader
